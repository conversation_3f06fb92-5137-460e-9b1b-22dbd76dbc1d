from typing import Annotated

from bson import ObjectId
from fastapi import APIRouter, Request, Query, Depends
from fastapi.security import OAuth2PasswordRequestForm

from apps.common import permission_dp, unified_resp
from apps.common.http_base import RespModel
from apps.models.common import ObjectIdStr
import apps.models.user as UserModel
from apps.services.user import UserService
from apps.models.permissions import FuncId


router = APIRouter(prefix="/user", tags=["用户管理"], responses={})


@router.get("/", dependencies=permission_dp(FuncId.UserManage), response_model=RespModel[UserModel.UserListOut])
@unified_resp
async def user_list(_: Request, q: Annotated[UserModel.Query, Query()]):
    return await UserService().list(q)


@router.post("/", dependencies=permission_dp(FuncId.UserManage), response_model=RespModel)
@unified_resp
async def user_add(_: Request, user: UserModel.Create):
    user_srv = UserService()
    return await user_srv.create(user)


@router.put("/", dependencies=permission_dp(FuncId.UserManage), response_model=RespModel)
@unified_resp
async def user_info_update(_: Request, user: UserModel.Update):
    user_srv = UserService()
    return await user_srv.update(user)


@router.delete("/", dependencies=permission_dp(FuncId.UserManage), response_model=RespModel)
@unified_resp
async def user_delete(_: Request, uid: ObjectIdStr):
    user_srv = UserService()
    return await user_srv.delete(uid=uid)


@router.get("/detail", dependencies=permission_dp(FuncId.UserManage), response_model=RespModel)
@unified_resp
async def user_detail(_: Request, uid: ObjectIdStr):
    user_srv = UserService()
    return await user_srv.info(uid=uid)


@router.put("/status", dependencies=permission_dp(FuncId.UserManage), response_model=RespModel)
@unified_resp
async def user_status_update(_: Request, user: UserModel.ChangeStatus):
    user_srv = UserService()
    return await user_srv.update_status(uid=user.id, status=user.status)


@router.post("/reset_password", dependencies=permission_dp(FuncId.UserManage), response_model=RespModel)
@unified_resp
async def send_reset_password(_: Request, u_info: UserModel.Id):
    user_srv = UserService()
    return await user_srv.send_reset_password(u_info.id)


#################### 登录登出 ####################
@router.post("/login")
@unified_resp
async def login(req: Request, data: UserModel.Login):
    user_srv = UserService()
    return await user_srv.login(data, req)


@router.post("/auth")
async def auth(form_data: OAuth2PasswordRequestForm = Depends()):
    """兼容OAuth2认证, 同login接口功能一致"""
    data = UserModel.Login(
        grant_type=form_data.grant_type,
        username=form_data.username,
        password=form_data.password,
    )
    user_srv = UserService()
    res_data = await user_srv.login(data)
    return {"access_token": res_data["token"], "token_type": "bearer"}


@router.get("/logout")
@unified_resp
async def logout(req: Request):
    user_srv = UserService()
    return await user_srv.logout(req)


#################### KEY 管理 ####################
@router.post("/api_keys")
@unified_resp
async def create_api_keys(req: Request, data: UserModel.ApiKeyCreate):
    user: UserModel.CacheInfo = req.state.user
    user_srv = UserService()
    return await user_srv.create_api_key(user.id, data)


@router.delete("/api_keys")
@unified_resp
async def delete_api_keys(req: Request, token: str = Query(...)):
    user: UserModel.CacheInfo = req.state.user
    user_srv = UserService()
    return await user_srv.delete_api_key(user.id, token)


@router.get("/api_keys")
@unified_resp
async def list_api_keys(req: Request):
    user: UserModel.CacheInfo = req.state.user
    user_srv = UserService()
    return await user_srv.list_api_key(user.id)


#################### 自身部分接口 ####################
@router.get("/info")
@unified_resp
async def info(req: Request):
    user_srv = UserService()
    return await user_srv.detail(req)


@router.get("/me")
@unified_resp
async def user_detail_self(req: Request):
    user_srv = UserService()
    return await user_srv.detail(req)


@router.put("/me")
@unified_resp
async def user_update(req: Request, user: UserModel.Update):
    user.id = ObjectId(req.state.user.id)
    user_srv = UserService()
    return await user_srv.update(user)


@router.put("/me/password")
@unified_resp
async def user_update_password(req: Request, user: UserModel.ChangePass):
    user_id = req.state.user.id
    user_srv = UserService()
    return await user_srv.update_password(ObjectId(user_id), user.new_password, user.old_password)


@router.post("/me/reset_password")
@unified_resp
async def reset_password(_: Request, data: UserModel.ResetPass):
    user_srv = UserService()
    return await user_srv.reset_password(data.token, data.password)
