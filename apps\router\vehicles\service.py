from typing import Annotated

import httpx
from fastapi import APIRouter, Request, Query, Header
from fastapi.responses import Response, FileResponse, StreamingResponse

from apps.common import permission_dp, unified_resp, HttpResp, AppException
from apps.models.common import ObjectIdStr
import apps.models.vehicle as VModel
import apps.models.data_man as DModel
import apps.services.vehicles as VehicleService
import apps.services.data_man as DMService
from apps.models.permissions import FuncId, RoleResourceFlag


router = APIRouter(prefix="/vehicles/{vehicle_id}/service", tags=["车辆服务"])
hclient = httpx.AsyncClient()


@router.get("/cameras", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR))
@unified_resp
async def vehicle_enable_cameras(_: Request, vehicle_id: ObjectIdStr):
    """获取车辆的可用相机列表"""
    srv = VehicleService.Camera(vehicle_id)
    data = await srv.get_enable_cam()
    return data


@router.post("/mix_template", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR))
@unified_resp
async def template_add(_: Request, vehicle_id: ObjectIdStr, templ_data: VModel.MixLayoutTemplate):
    """新增混流模版"""
    srv = VehicleService.MixTemplate(vehicle_id)
    return await srv.add(templ_data)


@router.delete("/mix_template", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR))
@unified_resp
async def template_delete(_: Request, vehicle_id: ObjectIdStr, template_name: str):
    """删除混流模版"""
    srv = VehicleService.MixTemplate(vehicle_id)
    return await srv.delete(template_name)


@router.put("/mix_template", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR))
@unified_resp
async def template_update(_: Request, vehicle_id: ObjectIdStr, templ_data: VModel.MixLayoutTemplate):
    """更新混流模版"""
    srv = VehicleService.MixTemplate(vehicle_id)
    return await srv.update(templ_data)


@router.get("/mix_template", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR))
@unified_resp
async def template_list(_: Request, vehicle_id: ObjectIdStr):
    """混流模版列表"""
    srv = VehicleService.MixTemplate(vehicle_id)
    return await srv.query()


@router.get("/mix_layout", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR))
@unified_resp
async def get_mix_stream_update(_: Request, vehicle_id: ObjectIdStr):
    """获取当前混流布局"""
    return await VehicleService.get_mix_layout(vehicle_id)


@router.put("/mix_layout", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR))
@unified_resp
async def set_mix_stream_update(
    _: Request,
    vehicle_id: ObjectIdStr,
    data: VModel.MixLayout,
):
    """混流服务，主要用于更新混流接口，可以不通过节点参数修改来更新"""
    return await VehicleService.update_mix_layout(vehicle_id, data)


@router.get("/live_stream/{end_path}", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.VIEWER))
async def play_live_stream(req: Request, vehicle_id: ObjectIdStr, end_path: str = "index.m3u8"):
    """车辆的直播流地址"""
    if end_path.endswith(".m3u8"):
        file_type = "application/vnd.apple.mpegurl"
    elif end_path.endswith(".mp4"):
        file_type = "video/mp4"
    else:
        raise AppException(HttpResp.HLS_SERVER_ERROR)

    hls_url = await VehicleService.get_hls_url(vehicle_id)
    if hls_url is None:
        raise AppException(HttpResp.HLS_NOT_FOUND)
    hls_url = f"{hls_url.strip('/')}/{end_path}"

    try:
        res = await hclient.get(hls_url, headers=req.headers, params=req.query_params)
    except httpx.HTTPError as e:
        raise AppException(HttpResp.HLS_SERVER_ERROR) from e

    if res.status_code == 200:
        response = Response(
            content=res.content,
            headers={"Content-Type": file_type, "Content-Length": str(len(res.content))},
        )
        return response
    if res.status_code == 404:
        raise AppException(HttpResp.HLS_NOT_FOUND)

    raise AppException(HttpResp.HLS_SERVER_ERROR)


@router.get("/video", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.VIEWER))
@unified_resp
async def get_record_video(_: Request, vehicle_id: ObjectIdStr, q: Annotated[DModel.VideoQuery, Query()]):
    """获取车辆的录像列表"""
    q.vehicle_id = vehicle_id
    return await DMService.query_video_data(q)


@router.get("/video/play/{video_id}.mp4")
async def video_segment_play(_: Request, video_id: ObjectIdStr, range: str = Header(None)) -> StreamingResponse:
    return await DMService.get_video_segment_data(video_id, range)


@router.get("/video/download/{video_id}.mp4")
async def video_segment_download(_: Request, video_id: ObjectIdStr) -> FileResponse:
    video = await DMService.get_video_data_by_id(video_id)
    if not video.exists:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "video segment file not found.")
    return FileResponse(video.path, media_type="video/mp4")


@router.get("/video/cover/{video_id}.jpg")
async def video_segment_cover_get(_: Request, video_id: ObjectIdStr) -> FileResponse:
    video = await DMService.get_video_data_by_id(video_id)
    if not video.cover_exists:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "video cover data not found.")
    return FileResponse(video.cover_path, media_type="image/jpeg")


@router.get("/signal/joystick", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.VIEWER))
@unified_resp
async def get_joystick_data(_: Request, vehicle_id: ObjectIdStr, q: Annotated[DModel.TimeRangeQuery, Query()]):
    """获取车辆控制数据"""
    return await DMService.get_joystick_data(vehicle_id, q)


@router.get("/signal/status", dependencies=permission_dp(FuncId.VehicleView, RoleResourceFlag.VIEWER))
@unified_resp
async def get_status_data(_: Request, vehicle_id: ObjectIdStr, q: Annotated[DModel.TimeRangeQuery, Query()]):
    """获取车辆控制数据"""
    pass
