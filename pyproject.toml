[project]
name = "oms-api"
version = "1.9.1"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiocsv==1.3.2",
    "aiohappyeyeballs==2.4.4",
    "aiohttp==3.11.11",
    "aiosignal==1.3.2",
    "annotated-types==0.7.0",
    "anyio==4.8.0",
    "arrow==1.3.0",
    "attrs==24.3.0",
    "broadcaster==0.3.1",
    "certifi==2025.1.31",
    "click==8.1.8",
    "colorama==0.4.6",
    "dnspython==2.7.0",
    "email-validator==2.2.0",
    "fastapi-cli[standard]==0.0.7",
    "fastapi[standard]==0.115.11",
    "frozenlist==1.5.0",
    "h11==0.14.0",
    "httpcore==1.0.7",
    "httptools==0.6.4",
    "httpx==0.28.1",
    "idna==3.10",
    "influxdb-client==1.48.0",
    "jinja2==3.1.6",
    "markdown-it-py==3.0.0",
    "markupsafe==3.0.2",
    "mdurl==0.1.2",
    "motor==3.6.0",
    "multidict==6.1.0",
    "orjson==3.10.15",
    "propcache==0.2.1",
    "pydantic[email]==2.10.6",
    "pydantic-core==2.27.2",
    "pydantic-settings==2.7.1",
    "pygments==2.19.1",
    "pymongo==4.9.2",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.0.1",
    "python-multipart==0.0.20",
    "pyyaml==6.0.2",
    "reactivex==4.0.4",
    "redis==5.2.1",
    "rich-toolkit==0.13.2",
    "rich==13.9.4",
    "setuptools==75.8.0",
    "shellingham==1.5.4",
    "six==1.17.0",
    "sniffio==1.3.1",
    "starlette==0.46.1",
    "typer==0.15.2",
    "types-python-dateutil==2.9.0.20241206",
    "typing-extensions==4.12.2",
    "urllib3==2.3.0",
    "uvicorn[standard]==0.34.0",
    "uvloop==0.21.0",
    "watchfiles==1.0.4",
    "websockets==15.0.1",
    "yarl==1.18.3",
    "packaging==24.2",
    "pathspec==0.12.1",
    "platformdirs==4.3.6",
]


[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[tool.ruff]
line-length = 120

[tool.ruff.lint.isort]
length-sort = true

[tool.black]
line-length = 120
target-version = ['py312']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''
