import { i18n } from "@/locales/setupI18n";
import { El<PERSON>essage, ElMessageBox, ElNotification, ElLoading, type ElMessageBoxOptions } from "element-plus";
import type { LoadingInstance } from "element-plus/es/components/loading/src/loading";

const logoSvg = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="167px" height="167px" viewBox="0 0 167 167" enable-background="new 0 0 167 167" xml:space="preserve">  <image id="image0" width="167" height="167" x="0" y="0"
    xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKcAAACnCAYAAAB0FkzsAAAAIGNIUk0AAHomAACAhAAA+gAAAIDo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=" />
</svg>
`;

export class Feedback {
  private loadingInstance: LoadingInstance | null = null;
  static instance: Feedback | null = null;
  static getInstance() {
    return this.instance ?? (this.instance = new Feedback());
  }
  // 消息提示
  msg(msg: string) {
    ElMessage.info(msg);
  }
  // 错误消息
  msgError(msg: string) {
    ElMessage.error(msg);
  }
  // 成功消息
  msgSuccess(msg: string) {
    ElMessage.success(msg);
  }
  // 警告消息
  msgWarning(msg: string) {
    ElMessage.warning(msg);
  }
  // 弹出提示
  alert(msg: string) {
    ElMessageBox.alert(msg, "系统提示");
  }
  // 错误提示
  alertError(msg: string) {
    ElMessageBox.alert(msg, "系统提示", { type: "error" });
  }
  // 成功提示
  alertSuccess(msg: string) {
    ElMessageBox.alert(msg, "系统提示", { type: "success" });
  }
  // 警告提示
  alertWarning(msg: string) {
    ElMessageBox.alert(msg, "系统提示", { type: "warning" });
  }
  // 通知提示
  notify(msg: string) {
    ElNotification.info(msg);
  }
  // 错误通知
  notifyError(msg: string) {
    ElNotification.error(msg);
    // ElNotification({
    //   title: "错误提示",
    //   message: msg,
    //   type: "error",
    //   duration: 0,
    // });
  }
  // 成功通知
  notifySuccess(msg: string) {
    ElNotification.success(msg);
  }
  // 警告通知
  notifyWarning(msg: string) {
    ElNotification.warning(msg);
  }
  // 确认窗体
  confirm(msg: string) {
    return ElMessageBox.confirm(msg, i18n.global.t("vehicle.温馨提示"), {
      confirmButtonText: i18n.global.t("vehicle.确定"),
      cancelButtonText: i18n.global.t("vehicle.取消"),
      type: "warning",
    });
  }
  // 提交内容
  prompt(title: string, content: string, options?: ElMessageBoxOptions) {
    return ElMessageBox.prompt(content, title, {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      ...options,
    });
  }
  // 打开全局loading
  loading(msg: string) {
    this.loadingInstance = ElLoading.service({
      lock: true,
      text: msg,
      svg: logoSvg,
      svgViewBox: "0 0 167 167",
    });
  }
  // 关闭全局loading
  closeLoading() {
    this.loadingInstance?.close();
  }
}

const feedback = Feedback.getInstance();

export default feedback;
