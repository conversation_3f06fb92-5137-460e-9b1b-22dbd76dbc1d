<template>
  <el-scrollbar :height="scrollHeight">
    <div class="gmsl-wrap">
      <!-- gmsl相机 -->
      <div
        v-if="cameraType === 1"
        class="gmsl-item flex flex-col items-center justify-center"
        v-for="(item, index) in gmslList"
        :key="index"
      >
        <div>{{ item.id }}</div>
        <div class="h-[90px]">
          <el-image style="width: 90px; height: 90px" v-if="item.type !== 'MVG2CB-NONE'" :src="camera" fit="fill" />
          <div v-else class="leading-[90px]">{{ $t("vehicle.请选择") }}</div>
        </div>
        <el-form label-width="auto">
          <el-form-item :label="$t('vehicle.名称')" class="!mb-1">
            <el-input v-model="item.camera_name" :placeholder="$t('vehicle.请输入相机名称')" />
          </el-form-item>
          <el-form-item :label="`I D`" class="!mb-1">
            <el-input v-model="item.camera_id" :placeholder="$t('vehicle.请输入相机ID')" />
          </el-form-item>
          <el-form-item label="格式" class="!mb-1">
            <el-input v-model="item.format" placeholder="请输入格式" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.类型')" class="!mb-1">
            <el-select v-model="item.type" :placeholder="$t('vehicle.请选择')">
              <el-option
                v-for="option in CAMERA_TYPE_LIST"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 网络相机 -->
      <div
        v-if="cameraType === 2"
        class="gmsl-item flex flex-col items-center justify-center relative"
        v-for="(item, index) in networkList"
        :key="index"
      >
        <Icon
          v-if="index === networkList.length - 1"
          class="absolute right-2 top-2 w-6 cursor-pointer"
          :size="18"
          name="el-icon-Delete"
          @click="handleDeleteCan(item.id)"
        />
        <div>{{ item.id }}</div>
        <div class="h-[90px]">
          <el-image style="width: 90px; height: 90px" v-if="item.uri !== ''" :src="camera" fit="fill" />
          <div v-else class="leading-[90px]">{{ $t("vehicle.请配置") }}</div>
        </div>
        <el-form label-width="auto">
          <el-form-item :label="$t('vehicle.名称')">
            <el-input v-model="item.camera_name" :placeholder="$t('vehicle.请输入相机名称')" />
          </el-form-item>
          <el-form-item :label="`I D`">
            <el-input v-model="item.camera_id" :placeholder="$t('vehicle.请输入相机ID')" />
          </el-form-item>
          <el-form-item label="格式">
            <el-input v-model="item.format" :placeholder="$t('vehicle.请输入')" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.地址')">
            <el-input v-model="item.uri" :placeholder="$t('vehicle.请输入相机地址')" />
          </el-form-item>
        </el-form>
      </div>

      <div
        v-if="networkList.length < 8 && cameraType === 2"
        class="gmsl-item flex flex-col items-center justify-center cursor-pointer min-h-[200px]"
        @click="addCanItem"
      >
        {{ $t("vehicle.添加") }}
      </div>

      <!-- 相机状态 -->
      <div
        v-if="cameraType === 99"
        class="gmsl-item flex flex-col items-center justify-center"
        style="flex-basis: 20%"
        v-for="(item, index) in statusList"
        :key="index"
      >
        <div>{{ item.id }}</div>
        <div class="h-[90px]">
          <el-image
            style="width: 90px; height: 90px"
            v-if="item.camera_name && item.camera_name !== ''"
            :src="camera"
            fit="fill"
          />
          <div v-else class="leading-[90px]">{{ $t("vehicle.请配置") }}</div>
        </div>
        <div>{{ item.camera_name }}</div>
        <div class="w-full flex items-center justify-center h-[60px]">
          <span class="block mr-1">{{ $t("vehicle.是否启动") }}</span>
          <el-switch :disabled="item.camera_name === '' && !item.is_enable" v-model="item.is_enable" />
        </div>
      </div>

      <div class="w-full flex justify-end mt-4">
        <el-button type="primary" @click="handleSave">{{ $t("vehicle.保存") }} </el-button>
      </div>
    </div>
  </el-scrollbar>
</template>
<script lang="ts" setup camera_name="gmsl">
import { CAMERA_TYPE_LIST } from "@/utils/constants";
import feedback from "@/utils/feedback";
import camera from "@/assets/images/sensing_gmsl2.png";

import { cameraListApi, cameraEdit, getCameraStatus, setCameraStatus } from "@/api/device";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();

const props = defineProps<{
  vehicleId: string;
  cameraType: number;
}>();

const scrollHeight = computed(() => {
  return document.documentElement.clientHeight - 158;
});

const handleSave = async () => {
  const flag = checkObj();
  if (!flag) return;

  switch (props.cameraType) {
    case 1:
      if (!checkDuplicateCamera(gmslList.value)) return;
      await cameraEdit({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
        camera_list: gmslList.value,
      });
      break;

    case 2:
      if (!checkDuplicateCamera(networkList.value)) return;
      await cameraEdit({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
        camera_list: networkList.value,
      });
      break;

    case 99:
      await setCameraStatus({
        vehicle_id: props.vehicleId,
        camera_list: statusList.value,
      });
      break;

    default:
      break;
  }

  feedback.msgSuccess("保存成功");
};

const checkObj = () => {
  let flag = true;
  gmslList.value.forEach((item) => {
    if (
      !(
        !(item.camera_id == "" || item.camera_name == "" || item.type == "MVG2CB-NONE") ||
        (item.camera_id == "" && item.camera_name == "" && item.type == "MVG2CB-NONE")
      )
    ) {
      feedback.msgError(`${item.id} 相机ID、名称和类型不能为空`);
      flag = false;
    }
  });
  networkList.value.forEach((item: any) => {
    if (
      !(
        !(item.camera_id == "" || item.camera_name == "" || item.type == "MVG2CB-NONE") ||
        (item.camera_id == "" && item.camera_name == "" && item.type == "MVG2CB-NONE")
      )
    ) {
      feedback.msgError(`${item.id} 相机ID、名称和类型不能为空`);
      flag = false;
    }
  });
  return flag;
};

const checkDuplicateCamera = (cameraList: any) => {
  const cameraIds = new Set();
  const cameraNames = new Set();

  for (const camera of cameraList) {
    if (camera.camera_id && cameraIds.has(camera.camera_id)) {
      feedback.msgError(`相机ID重复: ${camera.camera_id}`);
      return false;
    }
    if (camera.camera_name && cameraNames.has(camera.camera_name)) {
      feedback.msgError(`相机名称重复: ${camera.camera_name}`);
      return false;
    }
    cameraIds.add(camera.camera_id);
    cameraNames.add(camera.camera_name);
  }

  return true;
};

const gmslList = ref([
  // 网络摄像头 地址 名字 宽高 是否启动
  {
    id: "VIDEO0",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
  {
    id: "VIDEO1",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
  {
    id: "VIDEO2",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
  {
    id: "VIDEO3",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
  {
    id: "VIDEO4",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
  {
    id: "VIDEO5",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
  {
    id: "VIDEO6",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
  {
    id: "VIDEO7",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "",
  },
]);

const networkList: any = ref([]);

const statusList: any = ref([
  {
    id: "VIDEO0",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO1",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO2",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO3",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO4",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO5",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO6",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO7",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
]);

const getCameraList = async () => {
  switch (props.cameraType) {
    case 1:
      const { lists: list } = await cameraListApi({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
      });
      list.forEach((item: any) => {
        gmslList.value.forEach((camera) => {
          if (camera.id === item.id) {
            Object.assign(camera, item);
          }
        });
      });
      break;

    case 2:
      const { lists: list1 } = await cameraListApi({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
      });
      networkList.value = list1;
      break;

    case 99:
      const { lists: list2 } = await getCameraStatus({
        vehicle_id: props.vehicleId,
      });
      if (list2.length > 0) statusList.value = list2;
      break;

    default:
      break;
  }
};

const addCanItem = () => {
  networkList.value.push({
    id: "NETWORK" + networkList.value.length,
    camera_id: "",
    camera_name: "",
    format: "",
    uri: "",
    is_enable: false,
  });
};

const handleDeleteCan = (id: string) => {
  const flag = networkList.value.findIndex((item: any) => item.id == id);
  if (flag !== -1) networkList.value.splice(flag, 1);
};

onMounted(() => {
  getCameraList();
});
</script>
<style lang="scss" scoped>
.gmsl-wrap {
  display: flex;
  flex-wrap: wrap;
  .gmsl-item {
    flex-basis: 25%;
    padding: 4px;
    background-color: #fcfcfc;
    border: 1px solid #e7e7e7;
  }
}
</style>
