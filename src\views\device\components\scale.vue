<template>
  <popup
    custom-class="no-footer camera-scale-popup"
    ref="popupRef"
    :title="title"
    :async="false"
    :width="String(pageData.containerWidth + 32)"
    :confirmButtonText="$t('vehicle.确定')"
    :customButtonText="customButtonText"
    :cancelButtonText="false"
    @confirm="handleSubmit"
    @close="handleClose"
    @custom="resetScale"
  >
    <div>
      <span>比例调整为</span>
      <el-button type="primary" link @click="handleRatioChange(1)">1:1</el-button>
    </div>
    <div
      ref="container"
      class="container"
      :style="containerStyle"
      @mousedown="startDrawing"
      @mousemove="handleDrawMove"
      @mouseup="stopDrawing"
    >
      <div v-if="selectedBox" class="box" :style="boxStyle" @mousedown="startMoving">
        <div class="resize-handle" @mousedown.stop="startResizing" />
      </div>
      <div :class="`container-bg container-bg-${pageData.rotateEnum}`"></div>
    </div>
  </popup>
</template>

<script lang="ts" setup>
import Popup from "@/components/popup/index.vue";
import { ref, reactive, computed, onUnmounted, type CSSProperties } from "vue";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();

const emit = defineEmits(["success", "close"]);
const popupRef = shallowRef<InstanceType<typeof Popup>>();

interface BoxDimensions {
  x: number;
  y: number;
  width: number;
  height: number;
}

const container = ref<HTMLElement | null>(null);
const selectedBox = ref<BoxDimensions | null>(null);
const isDrawing = ref(false);
const isDragging = ref(false);
const isResizing = ref(false);
const pageData = reactive({
  containerWidth: 0,
  containerHeight: 0,
  rotateEnum: 0,
  cameraName: "",
  resolution: 4,
});
const aspectRatio = ref(16 / 9);
const containerStyle = computed<CSSProperties>(() => ({
  width: `${pageData.containerWidth + 2}px`,
  height: `${pageData.containerHeight + 2}px`,
}));

const title = computed(() => {
  if (selectedBox.value) {
    return t("stream.缩放");
  } else {
    return `${t("stream.缩放")}（${t("stream.请划选放大区域")}）`;
  }
});

const customButtonText = computed(() => {
  if (selectedBox.value) {
    return t("stream.取消缩放");
  } else {
    return "";
  }
});

// 绘制状态
interface DrawState {
  startX: number;
  startY: number;
}
const drawState = reactive<DrawState>({
  startX: 0,
  startY: 0,
});

// 移动状态
interface MoveState {
  initialX: number;
  initialY: number;
  boxLeft: number;
  boxTop: number;
}
const moveState = reactive<MoveState>({
  initialX: 0,
  initialY: 0,
  boxLeft: 0,
  boxTop: 0,
});

// 调整大小状态
interface ResizeState {
  startW: number;
  startH: number;
  startMouseX: number;
  startMouseY: number;
}
const resizeState = reactive<ResizeState>({
  startW: 0,
  startH: 0,
  startMouseX: 0,
  startMouseY: 0,
});

const boxStyle = computed<CSSProperties>(() => {
  if (!selectedBox.value) return {};
  return {
    left: `${selectedBox.value.x}px`,
    top: `${selectedBox.value.y}px`,
    width: `${selectedBox.value.width}px`,
    height: `${selectedBox.value.height}px`,
  };
});

// 绘制功能
function startDrawing(e: MouseEvent) {
  if (selectedBox.value || (e.target as HTMLElement).classList.contains("resize-handle")) return;

  const rect = container.value!.getBoundingClientRect();
  drawState.startX = e.clientX - rect.left;
  drawState.startY = e.clientY - rect.top;
  isDrawing.value = true;

  selectedBox.value = {
    x: drawState.startX,
    y: drawState.startY,
    width: 0,
    height: 0,
  };
}

function handleDrawMove(e: MouseEvent) {
  if (!isDrawing.value || !selectedBox.value) return;

  const rect = container.value!.getBoundingClientRect();
  const currentX = e.clientX - rect.left;
  const currentY = e.clientY - rect.top;

  let width = currentX - drawState.startX;
  let height = currentY - drawState.startY;

  // 保持比例
  if (Math.abs(width / height) > aspectRatio.value) {
    height = width / aspectRatio.value;
  } else {
    width = height * aspectRatio.value;
  }

  // 边界检查
  width = Math.min(width, rect.width - drawState.startX);
  height = Math.min(height, rect.height - drawState.startY);

  updateBoxSize(width, height);
}

function stopDrawing() {
  isDrawing.value = false;
}

// 移动功能
function startMoving(e: MouseEvent) {
  if ((e.target as HTMLElement).classList.contains("resize-handle")) return;

  e.stopPropagation();
  isDragging.value = true;

  moveState.initialX = e.clientX;
  moveState.initialY = e.clientY;

  moveState.boxLeft = selectedBox.value!.x;
  moveState.boxTop = selectedBox.value!.y;

  document.addEventListener("mousemove", handleMove);
  document.addEventListener("mouseup", stopMoving);
}

function handleMove(e: MouseEvent) {
  if (!isDragging.value || !selectedBox.value) return;

  const deltaX = e.clientX - moveState.initialX;
  const deltaY = e.clientY - moveState.initialY;

  const rect = container.value!.getBoundingClientRect();
  const boxWidth = selectedBox.value.width;
  const boxHeight = selectedBox.value.height;

  // 计算新位置并限制边界
  let newLeft = moveState.boxLeft + deltaX;
  let newTop = moveState.boxTop + deltaY;

  newLeft = Math.max(0, Math.min(newLeft, rect.width - boxWidth - 2));
  newTop = Math.max(0, Math.min(newTop, rect.height - boxHeight - 2));

  selectedBox.value.x = newLeft;
  selectedBox.value.y = newTop;
}

function stopMoving() {
  isDragging.value = false;
  document.removeEventListener("mousemove", handleMove);
  document.removeEventListener("mouseup", stopMoving);
}

// 调整大小功能
function startResizing(e: MouseEvent) {
  e.stopPropagation();
  if (!selectedBox.value) return;

  const rect = container.value!.getBoundingClientRect();

  resizeState.startW = selectedBox.value.width;
  resizeState.startH = selectedBox.value.height;
  resizeState.startMouseX = e.clientX;
  resizeState.startMouseY = e.clientY;

  document.addEventListener("mousemove", handleResize);
  document.addEventListener("mouseup", stopResizing);
}

function handleResize(e: MouseEvent) {
  if (!selectedBox.value) return;

  const deltaX = e.clientX - resizeState.startMouseX;
  const deltaY = e.clientY - resizeState.startMouseY;

  // 向量投影计算
  const proj = (deltaX * aspectRatio.value + deltaY) / (aspectRatio.value ** 2 + 1);
  let newW = resizeState.startW + proj * aspectRatio.value;
  let newH = resizeState.startH + proj;

  // 边界检查
  const maxW = pageData.containerWidth - selectedBox.value.x;
  const maxH = pageData.containerHeight - selectedBox.value.y;

  newW = Math.min(newW, maxW);
  newH = Math.min(newH, maxH);

  // 最小尺寸限制
  const minWidth = 50;
  newW = Math.max(newW, minWidth);
  newH = Math.max(newH, minWidth / aspectRatio.value);

  updateBoxSize(newW, newH);
}

function stopResizing() {
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", stopResizing);
}

function updateBoxSize(width: number, height: number) {
  if (!selectedBox.value) return;
  selectedBox.value.width = Math.max(width, 1);
  selectedBox.value.height = Math.max(height, 1);
}

const handleRatioChange = (type: number) => {
  if (type === 1) {
    aspectRatio.value = 1;
    if (selectedBox.value) {
      selectedBox.value.height = selectedBox.value.width;
    }
    
  }
};

const setParams = (row: any) => {
  const copy_row = JSON.parse(JSON.stringify(row));
  copy_row.containerWidth = copy_row.containerWidth / pageData.resolution;
  copy_row.containerHeight = copy_row.containerHeight / pageData.resolution;
  Object.assign(pageData, copy_row);
  aspectRatio.value = pageData.containerWidth / pageData.containerHeight;
  const {
    boxParams: { crop_left, crop_top, crop_width, crop_height },
  } = row;
  console.log(crop_width);

  if (crop_width !== 0 && crop_height !== 0 && crop_width !== undefined) {
    selectedBox.value = {
      x: crop_left / pageData.resolution || 0,
      y: crop_top / pageData.resolution || 0,
      width: crop_width / pageData.resolution || 50,
      height: crop_height / pageData.resolution || 50 / aspectRatio.value,
    };
  }
};

const open = () => {
  popupRef.value?.open();
};

const handleClose = () => {
  emit("close");
};

const resetScale = () => {
  selectedBox.value = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  };

  handleSubmit();
};
const handleSubmit = () => {
  let item = JSON.parse(JSON.stringify(selectedBox.value));
  let res: any = {
    crop_left: 0,
    crop_top: 0,
    crop_width: 0,
    crop_height: 0,
  };

  res.crop_left = item.x;
  res.crop_top = item.y;
  res.crop_width = item.width;
  res.crop_height = item.height;

  for (const key in res) {
    if (Object.prototype.hasOwnProperty.call(res, key)) {
      res[key] = Math.ceil(res[key] * pageData.resolution);
    }
  }

  emit("success", { cameraName: pageData.cameraName, ...res });
  handleClose();
};

onMounted(() => {});

onUnmounted(() => {
  document.removeEventListener("mousemove", handleMove);
  document.removeEventListener("mouseup", stopMoving);
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", stopResizing);
});
defineExpose({
  open,
  setParams,
});
</script>

<style>
.camera-scale-popup {
  .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
<style scoped>
.container {
  position: relative;
  overflow: hidden;
  border: 1px solid #ccc;
  z-index: 1;
}
.container-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: 0;
}
.container-bg-0 {
  background-image: url("/src/assets/images/excavator_background1.jpg");
}
.container-bg-1 {
  background-image: url("/src/assets/images/excavator_background2.jpg");
}
.container-bg-2 {
  background-image: url("/src/assets/images/excavator_background1.jpg");
  transform: rotate(180deg);
}
.container-bg-3 {
  background-image: url("/src/assets/images/excavator_background2.jpg");
  transform: rotate(180deg);
}

.box {
  position: absolute;
  z-index: 1;
  border: 2px solid var(--el-color-primary);
  cursor: move;
  background-color: rgba(0, 0, 0, 0.4);
}

.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: var(--el-color-primary);
  bottom: 0;
  right: 0;
  cursor: se-resize;
}
</style>
