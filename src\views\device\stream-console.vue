<template>
  <div class="edit-popup">
    <div class="aid-wrap flex-1 flex-col justify-center items-center">
      <p class="flex justify-center text-7xl pt-2 pb-1 text-[#d4d4d4]">
        {{ $t("stream.辅助画面控制面板") }}
      </p>
      <p class="flex justify-start text-base mb-2 ml-1 text-[#fff] h-[21px]">
        {{ pageData.curLayoutName ? `${$t("stream.布局名称")}:${pageData.curLayoutName}` : "" }}
      </p>
      <div class="flex flex-row justify-center items-center">
        <div
          class="ml-1 min-w-[800px] min-h-[450px] bg-[#ffefe6]"
          :class="`w-[${canvasInit.width}px] h-[${canvasInit.height}px]`"
        >
          <canvas id="canvas"></canvas>
        </div>
        <div
          class="tab-wrap !border-none flex flex-col"
          :style="`height:${canvasInit.height}px`"
          shadow="never"
        >
          <div class="tabs flex flex-row justify-between ml-2 mr-1">
            <div
              class="bux-button"
              :class="activeName === 'first' ? 'bux-button-confirm' : 'bux-button-primary'"
              :style="locale === 'ja' ? 'padding:0 0' : ''"
              @click="handleTabChange('first')"
            >
              {{ $t("stream.参数调整") }}
            </div>
            <div
              class="bux-button"
              :class="activeName === 'second' ? 'bux-button-confirm' : 'bux-button-primary'"
              :style="locale === 'ja' ? 'padding:0 0' : ''"
              @click="handleTabChange('second')"
            >
              {{ $t("stream.布局管理") }}
            </div>
            <div
              class="bux-button"
              :class="activeName === 'third' ? 'bux-button-confirm' : 'bux-button-primary'"
              :style="locale === 'ja' ? 'padding:0 0' : ''"
              @click="handleTabChange('third')"
            >
              {{ $t("stream.相机管理") }}
            </div>
          </div>
          <div class="tabs-content" v-if="activeName === 'first'">
            <el-form
              :label-width="labelWidth"
              ref="formRef"
              class="mb-[-16px]"
              :model="currentRect"
              :inline="false"
            >
              <el-form-item class="relative select-none" :label="$t('stream.宽度')">
                <el-input-number
                  class="w-[100px]"
                  :min="canvasInit.minWidth"
                  :max="1920"
                  v-model="currentRect.width"
                  @change="changeObjData($event, 'width')"
                />
                <span class="ml-1">{{ $t("stream.像素") }}</span>
                <Icon
                  class="absolute left-[-34px] top-8 cursor-pointer"
                  :size="18"
                  color="#dfdfdf"
                  :name="pageData.isLockWidthHeight ? 'el-icon-Lock' : 'el-icon-Unlock'"
                  @click="pageData.isLockWidthHeight = !pageData.isLockWidthHeight"
                />
              </el-form-item>
              <el-form-item class="select-none" :label="$t('stream.高度')">
                <el-input-number
                  class="w-[100px]"
                  :min="canvasInit.minHeight"
                  :max="1080"
                  v-model="currentRect.height"
                  @change="changeObjData($event, 'height')"
                />
                <span class="ml-1">{{ $t("stream.像素") }}</span>
              </el-form-item>
              <el-form-item :label="$t('stream.左边距')">
                <el-input-number
                  class="w-[100px]"
                  :min="0"
                  v-model="currentRect.left"
                  @keyup.enter.native="changeObjData($event, 'left')"
                  @change="changeObjData($event, 'left')"
                />
                <span class="ml-1">{{ $t("stream.像素") }}</span>
              </el-form-item>
              <el-form-item :label="$t('stream.上边距')">
                <el-input-number
                  class="w-[100px]"
                  :min="0"
                  v-model="currentRect.top"
                  @keyup.enter.native="changeObjData($event, 'top')"
                  @change="changeObjData($event, 'top')"
                />
                <span class="ml-1">{{ $t("stream.像素") }}</span>
              </el-form-item>
              <el-form-item :label="$t('stream.旋转角度')">
                <el-select
                  ref="select"
                  style="width: 100px"
                  v-model="currentRect.rotateEnum"
                  @change="changeObjData($event, 'rotateEnum')"
                >
                  <el-option label="0°" :value="0">0°</el-option>
                  <el-option label="90°" :value="1">90°</el-option>
                  <el-option label="180°" :value="2">180°</el-option>
                  <el-option label="270°" :value="3">270°</el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('stream.镜像翻转')">
                <el-select
                  ref="select"
                  style="width: 100px"
                  v-model="currentRect.flip"
                  @change="changeObjData($event, 'flip')"
                >
                  <el-option :label="$t('stream.不翻转')" :value="0"></el-option>
                  <el-option :label="$t('stream.垂直翻转')" :value="1"></el-option>
                  <el-option :label="$t('stream.水平翻转')" :value="2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('stream.边框宽度')">
                <el-select
                  ref="select"
                  style="width: 100px"
                  v-model="currentRect.border_width"
                  @change="changeObjData($event, 'border_width')"
                >
                  <el-option :label="$t('stream.无边框')" :value="0"></el-option>
                  <el-option :label="$t('stream.细')" :value="4"></el-option>
                  <el-option :label="$t('stream.中')" :value="6"></el-option>
                  <el-option :label="$t('stream.粗')" :value="10"></el-option>
                </el-select>
                <span class="ml-1">{{ $t("stream.像素") }}</span>
              </el-form-item>
              <el-form-item :label="$t('stream.边框颜色')">
                <el-color-picker
                  v-model="currentRect.border_color"
                  @change="changeBorderColor"
                  :show-alpha="false"
                  :predefine="predefineColors"
                  color-format="hex"
                />
                <div
                  class="ml-3 text-[12px] h-6 leading-6 px-1 text-white border-[1px] border-white rounded cursor-pointer"
                  @click="changeItemScale"
                >
                  {{ $t("stream.缩放调整") }}
                </div>
              </el-form-item>
            </el-form>
          </div>
          <div class="tabs-content" v-if="activeName === 'second'">
            <div>
              <el-scrollbar height="374px">
                <div
                  v-if="pageData.layoutList.length !== 0"
                  class="h-10 mb-1 flex flex-row justify-between items-center px-2 rounded-[4px] text mr-1"
                  :class="pageData.curLayoutName === item.name ? 'bg-primary-light-9' : ''"
                  v-for="item in pageData.layoutList"
                >
                  <div
                    class="name one-line-ellipsis max-w-[140px]"
                    :class="pageData.curLayoutName === item.name ? 'text-[#000]' : 'text-[#fff]'"
                  >
                    {{ item.name }}
                  </div>
                  <div class="button">
                    <el-button
                      class="w-6"
                      size="small"
                      type="primary"
                      @click="deleteLayout(item.name)"
                    >
                      <icon name="local-icon-shanchu" :size="18" />
                    </el-button>
                    <el-button class="w-6" size="small" type="primary" @click="useLayout(item)">
                      <icon name="local-icon-sousuo" :size="18" />
                    </el-button>
                  </div>
                </div>
                <div v-else class="flex justify-center items-center h-[314px]">
                  <el-empty :image-size="150" description="布局列表为空，请新建布局" />
                </div>
              </el-scrollbar>
              <div class="bux-button bux-button-confirm w-36 mx-auto" @click="newLayout()">
                {{ $t("stream.新增布局") }}
              </div>
            </div>
          </div>
          <div class="tabs-content" v-if="activeName === 'third'">
            <div class="flex flex-col h-[360px] mr-1">
              <div
                v-if="pageData.cameraList.length !== 0"
                class="h-10 mb-1 flex flex-row justify-between items-center px-1"
                v-for="item in pageData.cameraList"
              >
                <div class="name text-[#fff]">{{ item.detail }}</div>
                <div class="button-wrap">
                  <el-button
                    class="w-6"
                    type="primary"
                    size="small"
                    :disabled="isMainVideo(item)"
                    @click="setMainVideo(item)"
                  >
                    <icon name="local-icon-shouye_mian" :size="18" />
                  </el-button>
                  <el-button
                    v-if="isOnCanvas(item.device)"
                    :disabled="isMainVideo(item)"
                    class="w-6"
                    type="primary"
                    size="small"
                    @click="deleteObj(item.device)"
                  >
                    <icon name="local-icon-kejian" :size="18" />
                  </el-button>
                  <el-button
                    v-else
                    :disabled="isMainVideo(item)"
                    class="w-6"
                    type="primary"
                    size="small"
                    @click="addDefaultVideo(item)"
                  >
                    <icon name="local-icon-bukejian" :size="18" />
                  </el-button>
                </div>
              </div>
              <div v-else class="flex justify-center items-center h-[360px]">
                <el-empty :image-size="150" description="相机列表为空，请添加相机" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-row class="py-4 mr-2" justify="end">
        <div class="bux-button bux-button-cancel" @click="handleClose">
          {{ $t("stream.取消") }}
        </div>
        <div class="bux-button bux-button-primary ml-3" @click="ShowSaveDialog($event, 'save')">
          {{ $t("stream.另存为新布局") }}
        </div>
        <div
          v-if="pageData.curLayoutName"
          class="bux-button bux-button-primary ml-3"
          @click="handleUpdateLayout()"
        >
          {{ $t("stream.更新模版") }}
        </div>
        <div class="bux-button bux-button-confirm ml-3" @click="handleSubmit">
          {{ $t("stream.应用布局") }}
        </div>
      </el-row>
    </div>

    <el-dialog
      class="save-layout"
      v-model="pageData.dialogFormVisible"
      :title="pageData.dialogFormTitle"
      width="400px"
    >
      <div class="flex items-center">
        <div class="w-20">{{ $t("stream.布局名称") }}</div>
        <el-input v-model.trim="pageData.layoutName" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="mr-1" @click="pageData.dialogFormVisible = false">{{
            $t("stream.取消")
          }}</el-button>
          <el-button type="primary" @click="handleSave">{{ $t("stream.确定") }}</el-button>
        </span>
      </template>
    </el-dialog>
    <Scale
      ref="scaleRef"
      v-if="showScale"
      @close="showScale = false"
      @success="handleScaleSubmit"
    ></Scale>
  </div>
</template>
<script lang="ts" setup>
import { fabric } from "fabric";
import feedback from "@/utils/feedback";
import { CAMERA_TYPE_LIST } from "@/utils/constants";
import device_background from "@/assets/images/excavator_background1.jpg";
import {
  layoutList,
  layoutAdd,
  layoutEdit,
  layoutDelete,
  getEnableCameraList,
  getMixLayout,
  editMixLayout,
  metaDataDetail,
} from "@/api/device";
import type { LocaleType } from "@/locales/lang";
import { useLocale } from "@/locales/useLocale";
import { useI18n } from "vue-i18n";
import { TOKEN_KEY } from "@/enums/cacheEnums";
import useUserStore from "@/stores/modules/user";
import cache from "@/utils/cache";
import Scale from "./components/scale.vue";

const userStore = useUserStore();
const { changeLocale } = useLocale();
const route = useRoute();
let { AuthToken: token, lang, VehicleId: vehicleId } = route.query;
const { t, locale } = useI18n();
const scaleRef = ref<InstanceType<typeof Scale>>();
const showScale = ref(false);

const props = defineProps({
  token: {
    type: String,
    require: false,
  },
  lang: {
    type: String,
    require: false,
  },
  vehicleId: {
    type: String,
    require: false,
  },
});

if (props.token) token = props.token;
if (props.lang) lang = props.lang;
if (props.vehicleId) vehicleId = props.vehicleId;

// 定义自定义参数的接口
interface CustomGroupProps extends fabric.IGroupOptions {
  device: string;
}

const emit = defineEmits(["success", "close"]);

let canvas: any = null;

const angleEnum: any = {
  0: 0,
  1: 90,
  2: 180,
  3: 270,
};

const activeName = ref("first");

const canvasInit = reactive({
  width: 800,
  height: 450,
  backgroundColor: "#ffefe6",
  gridWidth: 25,
  minWidth: 120,
  maxWidth: 1080,
  minHeight: 80,
  maxHeight: 607.5,
});

const pageData: any = reactive({
  curRow: {},
  resolution: 2.4, // 绘制元素 720p需要除以1.6 1080p需要除以2.4
  dialogFormVisible: false,
  dialogFormTitle: "",
  layoutName: "",
  cameraList: [],
  initCameraList: [],
  canvasObjects: [],
  layoutList: [],
  main: {
    device: "",
    detail: "",
    width: 1920,
    height: 1080,
    left: 0,
    top: 0,
    rotate: 0,
    flip: 0,
    z_index: 0,
    primary: 1,
    border_width: 0,
    border_color: "#FF5F00",
  },
  curLayoutName: "",
  isLockWidthHeight: true,
  layoutBelong: "0", // 0: 所有模板 1: 我的模板 2: 公有模板
  vehicle_id: vehicleId,
  maxIndex: 1,
  layoutEdge: 24,
  consoleType: "1", // 1:三竖屏 2:新操作台
  country: "cn",
});

const currentRect = reactive({
  width: 10,
  height: 10,
  left: 0,
  top: 0,
  rotateEnum: 0,
  main: "0",
  flip: 0,
  zIndex: 1,
  border_color: "#FF5F00",
  border_width: 4,
});

const predefineColors = ref([
  "#FF5F00",
  "#ff8c00",
  "#ffd700",
  "#90ee90",
  "#00ced1",
  "#1e90ff",
  "#c71585",
]);

const isOnCanvas = computed(() => (item: any) => {
  return pageData.canvasObjects.includes(item);
});

const isMainVideo = computed(() => (item: any) => {
  return pageData.main.detail === item.detail;
});

const labelWidth = computed(() => {
  const locales: any = {
    "zh-CN": 70,
    en: 84,
    ja: 82,
  };

  return locales[locale.value];
});

const getMetaData = async () => {
  const { vehicle_id: vehicleId } = pageData;
  try {
    const { value } = await metaDataDetail({ key: "COUNTRY_CODE", vehicleId });
    pageData.country = value;
  } catch (error) {
    console.log(error);
  }
};

onMounted(async () => {
  let curLang = lang;
  if (lang == "zh_CN") curLang = "zh-CN";
  if (curLang) changeLocale(curLang as LocaleType);
  if (token) {
    userStore.token = token as string;
    cache.set(TOKEN_KEY, token);
  }
  const row = await getRowInfo();
  await getMetaData();
  await getLayoutData();
  await getCameraList();
  await init(row);
});

onBeforeUnmount(() => {
  if (canvas) {
    canvas.dispose();
  }
});

const getRowInfo = async () => {
  const { vehicle_id } = pageData;
  const cameraList = await getMixLayout(vehicle_id);
  return { camera_list: cameraList };
};

// 添加固定在右半边的元素
const addFixedRightElement = () => {
  // 创建一个矩形，占据右半边画布的一半
  const rect = new fabric.Rect({
    left: canvasInit.width / 2, // 从画布中间开始
    top: 0,
    width: canvasInit.width / 2, // 宽度为画布宽度的一半
    height: canvasInit.height,
    fill: "rgba(100, 100, 100, 0.7)", // 半透明灰色
    stroke: "#888888",
    strokeWidth: 1,
    strokeDashArray: [5, 5], // 虚线边框
    selectable: false, // 不可选择
    evented: false, // 不响应事件
    hoverCursor: "default", // 鼠标悬停时显示默认光标
  });

  // 添加文本标签
  const text = new fabric.Text(t("stream.置于屏幕左半侧"), {
    left: canvasInit.width * 0.75, // 放在右半区域的中间
    top: canvasInit.height / 2,
    fontSize: 20,
    textAlign: "center",
    originX: "center",
    originY: "center",
    fill: "#333333",
    selectable: false,
    evented: false,
  });

  // 创建一个组，包含矩形和文本
  const group = new fabric.Group([rect, text], {
    selectable: false, // 不可选择
    evented: false, // 不响应事件
  });

  // 添加到画布
  canvas.add(group);
  // 确保元素在最底层
  group.moveTo(0);
  canvas.renderAll();
};

const init = async (row: any) => {
  canvas = new fabric.Canvas("canvas", {
    width: canvasInit.width,
    height: canvasInit.height,
    backgroundColor: canvasInit.backgroundColor,
  });
  canvas.selection = false;
  pageData.curRow = JSON.parse(JSON.stringify(row));
  pageData.initCameraList = JSON.parse(JSON.stringify(pageData.curRow.camera_list));

  const arr: any = Object.values(row?.camera_list);
  render(arr); // 渲染已有元素
  // 在canvas中调用createGrid函数来创建网格
  createGrid(canvasInit.gridWidth, "rgba(0, 0, 0, 0.2)", 1);
  setTimeout(() => {
    canvas
      .getObjects()
      .filter((item: any) => item.zIndex)
      .sort((a: any, b: any) => {
        return a.zIndex - b.zIndex;
      })
      .forEach((target: any) => {
        target.moveTo(target.zIndex);
      });

    getCanvasDevice(); // 获取画布元素
  }, 300);

  if (pageData.country == "jp") {
    addFixedRightElement();
  }

  canvas.on("object:moving", objectMoving);
  canvas.on("object:scaling", objectScaling);
  canvas.on("object:rotating", objectRotating);
  canvas.on("mouse:down", selectionCreated);
  canvas.on("mouse:up", mouseup);
};

// 渲染已有元素
const render = (arr: any) => {
  arr.forEach(async (item: any, index: number) => {
    if (item.primary != 1) {
      if (index + 1 > pageData.maxIndex) pageData.maxIndex = index + 10;
      item.border_color = item.border_color || "#FF5F00";
      item.border_width = item.border_width || 0;
      item.zIndex = index + 1 || 1;
      await drawObj(item);
    } else {
      pageData.main = item;
    }
  });
};

// 绘制元素
const drawObj = (item: any) => {
  const rect: any = new fabric.Rect({
    top: item.top / pageData.resolution,
    left: item.left / pageData.resolution,
    width: (item.width - item.border_width) / pageData.resolution,
    height: (item.height - item.border_width) / pageData.resolution,
    fill: "transparent",
  });

  rect.set({
    stroke: item.border_color,
    strokeWidth: item.border_width / pageData.resolution,
    strokeDashArray: [],
  });

  const text = new fabric.Text(item.detail, {
    left: (item.left + item.width / 2) / pageData.resolution,
    top: (item.top + item.height / 2) / pageData.resolution - 10,
    fontSize: 20,
    textAlign: "center",
    originX: "center",
    originY: "center",
    fill: "#e5eaf3",
    shadow: "rgba(0, 0, 0, 0.5) 2px 2px 4px",
  });

  const textWidth = text.measureLine(0).width * pageData.resolution + 10;
  if (textWidth > item.width) {
    text.scaleX = item.width / textWidth;
  }

  const text1 = new fabric.Text(item.device, {
    left: (item.left + item.width / 2) / pageData.resolution,
    top: (item.top + item.height / 2) / pageData.resolution + 10,
    fontSize: 20,
    textAlign: "center",
    originX: "center",
    originY: "center",
    fill: "#e5eaf3",
    shadow: "rgba(0, 0, 0, 0.5) 2px 2px 4px",
  });

  const textWidth1 = text1.measureLine(0).width * pageData.resolution + 10;
  if (textWidth1 > item.width) {
    text1.scaleX = item.width / textWidth1;
  }
  if (item.height < 100) {
    text.scaleY = item.height / 200;
    text1.scaleY = item.height / 200;
  }

  fabric.Image.fromURL(device_background, function (img: any) {
    const initWidth = JSON.parse(JSON.stringify(img.width));
    const initHeight = JSON.parse(JSON.stringify(img.height));
    img.set({
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height,
      clipPath: rect,
      selectable: false,
      evented: false,
    });
    rect.fill = new fabric.Pattern({
      source: img.getElement(),
      offsetX: -(initWidth - rect.width) / 2,
      offsetY: -(initHeight - rect.height) / 2,
    });
    const group = new fabric.Group([rect, text, text1], {
      left: rect.left,
      top: rect.top,
      device: item.device,
      rotateEnum: item.rotate || 0,
      rotateEnumOld: item.rotate || 0,
      angle: angleEnum[item.rotate || 0],
      border_color: item.border_color || "#FF5F00",
      border_width: item.border_width || 0,
      z_index: item.z_index || 0,
      primary: item.primary || 0,
      zIndex: item.zIndex || 1,
      flip: item.flip || 0,
      lockScalingFlip: true,
    } as CustomGroupProps);
    setOrigin(group, "none");

    group.setControlsVisibility({
      mt: false, // 正上方控制点
      mb: false, // 正下方控制点
      ml: false, // 左侧控制点
      mr: false, // 右侧控制点
    });

    canvas?.add(markRaw(group));
  });
};

// 根据旋转角度设置顶点 type control(控制点) select（下拉框） none(不做动作)
const setOrigin = (item: any, type: any) => {
  const { rotateEnum, rotateEnumOld } = item;
  const width = item.getScaledWidth();
  const height = item.getScaledHeight();
  const { x, y } = item.getCenterPoint();

  function calculateRotatedParams(element: any, rotateEnumOld: number, rotateEnum: number) {
    const { crop_left, crop_top, crop_width, crop_height } = element;
    let params = { crop_left, crop_top, crop_width, crop_height };
    console.log("element", element, "rotateEnumOld:", rotateEnumOld, "rotateEnum:", rotateEnum);

    let objectCamera = pageData.cameraList.find((item: any) => item.device === element.device);

    let cameraItem = { width: 1920, height: 1080 };

    if (!objectCamera) return;
    if (objectCamera.cameraType === 3) {
      return feedback.msgError(t("stream.该相机不支持缩放调整"));
    }

    if (objectCamera.cameraType === 2) {
      cameraItem = { width: 1280, height: 720 };
    }

    if (objectCamera.cameraType === 1) {
      CAMERA_TYPE_LIST.forEach((item: any) => {
        if (item.device === objectCamera.device) {
          if (item.resolution === "N/A") {
            return feedback.msgError(t("stream.该相机不支持缩放调整"));
          }
          if (item.resolution === "1280*720") {
            cameraItem = { width: 1280, height: 720 };
          }
          if (item.resolution === "1920*1080") {
            cameraItem = { width: 1920, height: 1080 };
          }
        }
      });
    }
    const containerWidth = cameraItem.width;
    const containerHeight = cameraItem.height;

    // 直接使用rotateEnumOld和rotateEnum计算新的裁剪参数
    // 从rotateEnumOld到rotateEnum的转换
    // 0: 0度, 1: 90度, 2: 180度, 3: 270度
    if (rotateEnumOld === 0) {
      if (rotateEnum === 1) {
        params = {
          crop_left: containerHeight - crop_top - crop_height,
          crop_top: crop_left,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      } else if (rotateEnum === 2) {
        params = {
          crop_left: containerWidth - crop_left - crop_width,
          crop_top: containerHeight - crop_top - crop_height,
          crop_width: crop_width,
          crop_height: crop_height,
        };
      } else if (rotateEnum === 3) {
        params = {
          crop_left: crop_top,
          crop_top: containerWidth - crop_left - crop_width,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      }
    } else if (rotateEnumOld === 1) {
      if (rotateEnum === 0) {
        params = {
          crop_left: crop_top,
          crop_top: containerHeight - crop_left - crop_width,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      } else if (rotateEnum === 2) {
        params = {
          crop_left: containerWidth - crop_top - crop_height,
          crop_top: crop_left,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      } else if (rotateEnum === 3) {
        params = {
          crop_left: containerHeight - crop_left - crop_width,
          crop_top: containerWidth - crop_top - crop_height,
          crop_width: crop_width,
          crop_height: crop_height,
        };
      }
    } else if (rotateEnumOld === 2) {
      if (rotateEnum === 0) {
        params = {
          crop_left: containerWidth - crop_left - crop_width,
          crop_top: containerHeight - crop_top - crop_height,
          crop_width: crop_width,
          crop_height: crop_height,
        };
      } else if (rotateEnum === 1) {
        params = {
          crop_left: crop_top,
          crop_top: containerWidth - crop_left - crop_width,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      } else if (rotateEnum === 3) {
        params = {
          crop_left: containerHeight - crop_top - crop_height,
          crop_top: crop_left,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      }
    } else if (rotateEnumOld === 3) {
      if (rotateEnum === 0) {
        params = {
          crop_left: containerWidth - crop_top - crop_height,
          crop_top: crop_left,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      } else if (rotateEnum === 1) {
        params = {
          crop_left: containerHeight - crop_left - crop_width,
          crop_top: containerWidth - crop_top - crop_height,
          crop_width: crop_width,
          crop_height: crop_height,
        };
      } else if (rotateEnum === 2) {
        params = {
          crop_left: crop_top,
          crop_top: containerHeight - crop_left - crop_width,
          crop_width: crop_height,
          crop_height: crop_width,
        };
      }
    }
    return params;
  }

  const resetCenter = () => {
    const { x, y } = item.getCenterPoint();
    const oldCenterX = x - height / 2 + width / 2;
    const oldCenterY = y - width / 2 + height / 2;
    item.set({ left: oldCenterX - height / 2, top: oldCenterY - width / 2 });
  };
  const resetCenter180 = () => {
    const { x, y } = item.getCenterPoint();
    const oldCenterX = x - width / 2 + height / 2;
    const oldCenterY = y - height / 2 + width / 2;
    item.set({ left: oldCenterX - width / 2, top: oldCenterY - height / 2 });
  };
  if (rotateEnum === 1) {
    item.set({ originX: "left", originY: "bottom" });
    if (type === "control") item.set({ left: x - height / 2, top: y - width / 2 });
    if (type === "select") resetCenter();
  } else if (rotateEnum === 2) {
    item.set({ originX: "right", originY: "bottom" });
    if (type === "control") item.set({ left: x - width / 2, top: y - height / 2 });
    if (type === "select") resetCenter180();
  } else if (rotateEnum === 3) {
    item.set({ originX: "right", originY: "top" });
    if (type === "control") item.set({ left: x - height / 2, top: y - width / 2 });
    if (type === "select") resetCenter();
  } else {
    item.set({ originX: "left", originY: "top" });
    if (type === "control") item.set({ left: x - width / 2, top: y - height / 2 });
    if (type === "select") resetCenter180();
  }

  if (rotateEnum !== rotateEnumOld) {
    for (const key in pageData.initCameraList) {
      if (Object.prototype.hasOwnProperty.call(pageData.initCameraList, key)) {
        const element = pageData.initCameraList[key];
        if (
          element.device === item.device &&
          element.crop_width !== 0 &&
          element.crop_height !== 0
        ) {
          // 根据旋转差值转换坐标系
          const newParams = calculateRotatedParams(element, rotateEnumOld, rotateEnum);
          Object.assign(element, newParams);
          console.log("element", newParams);
        }
      }
    }
  }
  item.setCoords();
};

// 创建网格
const createGrid = (gridSize: number, color: any, opacity: any) => {
  const pathData = [];
  const width = canvas.getWidth();
  const height = canvas.getHeight();

  // 添加垂直线
  for (let i = 0; i < width / gridSize; i++) {
    const x = i * gridSize;
    pathData.push(`M ${x} 0 L ${x} ${height}`);
  }

  // 添加水平线
  for (let i = 0; i < height / gridSize; i++) {
    const y = i * gridSize;
    pathData.push(`M 0 ${y} L ${width} ${y}`);
  }

  const gridPath = new fabric.Path(pathData.join(" "), {
    stroke: color,
    strokeWidth: 1,
    opacity: opacity,
    selectable: false,
    evented: false,
  });
  canvas.add(gridPath);

  if (pageData.consoleType === "1") {
    const leftLine = new fabric.Line(
      [
        pageData.layoutEdge / pageData.resolution,
        0,
        pageData.layoutEdge / pageData.resolution,
        height,
      ],
      {
        stroke: "#A9A9A9", // 深灰色
        strokeWidth: 1,
        opacity: opacity,
        strokeDashArray: [5, 5], // 虚线
        selectable: false,
        evented: false,
      }
    );
    const rightLine = new fabric.Line(
      [
        width - pageData.layoutEdge / pageData.resolution,
        0,
        width - pageData.layoutEdge / pageData.resolution,
        height,
      ],
      {
        stroke: "#A9A9A9", // 深灰色
        strokeWidth: 1,
        opacity: opacity,
        strokeDashArray: [5, 5], // 虚线
        selectable: false,
        evented: false,
      }
    );
    canvas.add(leftLine);
    canvas.add(rightLine);
  }
};

// 选择事件处理
const selectionCreated = (e: any) => {
  const { target } = e;
  if (target === null) return;
  selectionUpdated(e);
};
// 移动事件处理
const objectMoving = (e: any) => {
  if (
    Math.round((e.target.left / canvasInit.gridWidth) * 2) % 2 == 0 &&
    Math.round((e.target.top / canvasInit.gridWidth) * 2) % 2 == 0
  ) {
    e.target
      .set({
        left: Math.round(e.target.left / canvasInit.gridWidth) * canvasInit.gridWidth,
        top: Math.round(e.target.top / canvasInit.gridWidth) * canvasInit.gridWidth,
      })
      .setCoords();
  }
  selectionUpdated(e);
};
// 缩放事件处理
const objectScaling = (e: any) => {
  const { target } = e;
  const isExchange = target.rotateEnum % 2 === 1;
  const scaleY = isExchange ? target.scaleX : target.scaleY;
  const scaleX = isExchange ? target.scaleY : target.scaleX;
  // 最小宽度
  if (target.width * target.scaleX * pageData.resolution < canvasInit.minWidth) {
    target.set({ scaleX: canvasInit.minWidth / (target.width * pageData.resolution) });
  }

  // 最小高度
  if (target.height * target.scaleY * pageData.resolution < canvasInit.minHeight) {
    target.set({ scaleY: canvasInit.minHeight / (target.height * pageData.resolution) });
  }

  // 最大宽度
  if (target.width * scaleX * pageData.resolution > canvasInit.maxWidth) {
    target.set({ scaleX: canvasInit.maxWidth / (target.width * pageData.resolution) });
  }

  // 最大高度
  if (target.height * scaleY * pageData.resolution > canvasInit.maxHeight) {
    target.set({ scaleY: canvasInit.maxHeight / (target.height * pageData.resolution) });
  }

  setTimeout(() => {
    selectionUpdated(e);
  }, 200);
};
// 旋转事件处理
const objectRotating = (e: any) => {};

// 松开鼠标触发边界检测
const mouseup = (e: any) => {
  const { target } = e;
  if (target === null) return;
  target.straighten();
  if (target.rotateEnum !== target.rotateEnumOld) {
    target.rotateEnumOld = target.rotateEnum;
  }
  const rotateEnum = Math.round((target.angle % 360) / 90) % 4; // 计算最近的90度的倍数
  target.set({ rotateEnum });
  target.zIndex = pageData.maxIndex++;
  target.moveTo(target.zIndex);
  setOrigin(target, "control");
  selectionUpdated(e);
  boundingDetection(e);
  if (pageData.country == "jp") {
    checkFixedAreaOverlap(target);
  }
};

// 边界检测
const boundingDetection = (e: any, isTarget = false) => {
  let { target } = e;
  if (isTarget) {
    target = e;
  }
  const boundingRect = target.getBoundingRect();
  const left = boundingRect.left;
  const top = boundingRect.top;
  const width = boundingRect.width;
  const height = boundingRect.height;

  // 确保元素不超出画布的左边界
  if (left < 0) {
    target.left -= left;
  }

  // 确保元素不超出画布的上边界
  if (top < 0) {
    target.top -= top;
  }

  // 确保元素不超出画布的右边界
  if (left + width > canvasInit.width) {
    target.left -= left + width - canvasInit.width;
  }

  // 确保元素不超出画布的下边界
  if (top + height > canvasInit.height) {
    target.top -= top + height - canvasInit.height;
  }

  // 更新元素的位置
  target.setCoords();

  // 调用renderAll方法应用更改
  canvas.renderAll();
};

// 检测元素是否与右侧固定区域重叠，如果重叠则将元素移回到固定区域外
const checkFixedAreaOverlap = (target: any) => {
  const boundingRect = target.getBoundingRect();
  const elementRight = boundingRect.left + boundingRect.width;

  const fixedAreaLeft = canvasInit.width / 2;
  // 检查元素是否与右侧固定区域重叠
  if (elementRight > fixedAreaLeft) {
    // 计算重叠部分的宽度
    const overlapWidth = elementRight - fixedAreaLeft;
    // 将元素向左移动，使其不再与固定区域重叠
    target.left -= overlapWidth;

    target.setCoords();
    canvas.renderAll();
    // feedback.msgWarning(t("stream.元素不能移动到右侧固定区域"));
  }
};

// 元素参数更新
const selectionUpdated = (e: any) => {
  const { target } = e;
  if (target === null) return;
  Object.assign(currentRect, getCurParams(target));
};

const getCurParams = (target: any) => {
  const device = target.device;
  const boundingRect = target.getBoundingRect();
  const width = boundingRect.width;
  const height = boundingRect.height;
  const left = target.left;
  const top = target.top;
  const rotateEnum = target.rotateEnum;
  const border_color = target.border_color;
  const border_width = target.border_width;
  const zIndex = target.zIndex;
  const flip = target.flip;

  let obj: any = {};
  obj = {
    device,
    width: Math.round(width * pageData.resolution),
    height: Math.round(height * pageData.resolution),
    left: Math.round(left * pageData.resolution),
    top: Math.round(top * pageData.resolution),
    border_color,
    border_width,
    rotateEnum,
    flip,
    zIndex,
  };

  return obj;
};

const changeBorderColor = (type: any) => {
  let activeObjects = canvas.getActiveObjects();
  // 如果点击位置下没有元素或者选中多个，退出函数
  let activeObject = activeObjects[0];
  if (!activeObject || activeObjects.length > 1) return;

  // 检查选中对象是否为组
  if (activeObject && activeObject.type === "group") {
    activeObject.border_color = type;
    // 遍历组中的每个对象
    activeObject.forEachObject(function (obj: any) {
      // 检查对象是否为矩形
      if (obj.type === "rect") {
        obj.set({
          stroke: type,
          strokeWidth: activeObject.border_width / pageData.resolution,
          strokeDashArray: [],
        });
      }
    });
    // 重新渲染画布
    canvas.renderAll();
  }
};

const changeBorderWidth = (width: any) => {
  let object = canvas.getActiveObjects()[0];
  const deviceName = JSON.parse(JSON.stringify(object.device));

  canvas.getObjects().forEach((i: any) => {
    if (i.device === deviceName) {
      const item: any = getCanvasObjs().find((item: any) => item.device === deviceName);
      if (!item) return;
      canvas.remove(i);
      item.border_width = width;
      addObj(item);
    }
  });

  setTimeout(() => {
    canvas.getObjects().forEach((t: any) => {
      if (deviceName === t.device) {
        canvas.setActiveObject(t);
      }
    });
  }, 100);
};

// 手动调整参数
const changeObjData = (e: any, type: string) => {
  let val;
  if (typeof e !== "number") {
    val = e.target.value / pageData.resolution;
  } else {
    val = e / pageData.resolution;
  }
  let activeObjects = canvas.getActiveObjects();
  // 如果点击位置下没有元素或者选中多个，退出函数
  let activeObject = activeObjects[0];
  if (!activeObject || activeObjects.length > 1) return;

  const { rotateEnum } = activeObject;
  const isExchange = rotateEnum % 2 === 1;
  // 缩放值 =  输入的宽度/缩放值 / 元素的宽度
  if (type === "width" && !isExchange) {
    activeObject.set({ scaleX: val / activeObject.width });
    if (pageData.isLockWidthHeight) {
      activeObject.set({ scaleY: ((val / 16) * 9) / activeObject.height });
      currentRect.height = Math.round((val / 16) * 9 * pageData.resolution);
    }
  }
  if (type === "width" && isExchange) {
    activeObject.set({ scaleY: val / activeObject.height });
    if (pageData.isLockWidthHeight) {
      activeObject.set({ scaleX: ((val / 9) * 16) / activeObject.width });
      currentRect.height = Math.round((val / 9) * 16 * pageData.resolution);
    }
  }
  if (type === "height" && !isExchange) {
    activeObject.set({ scaleY: val / activeObject.height });
    if (pageData.isLockWidthHeight) {
      activeObject.set({ scaleX: ((val / 9) * 16) / activeObject.width });
      currentRect.width = Math.round((val / 9) * 16 * pageData.resolution);
    }
  }
  if (type === "height" && isExchange) {
    activeObject.set({ scaleX: val / activeObject.width });
    if (pageData.isLockWidthHeight) {
      activeObject.set({ scaleY: ((val / 16) * 9) / activeObject.height });
      currentRect.width = Math.round((val / 16) * 9 * pageData.resolution);
    }
  }
  if (type === "left") {
    activeObject.set({ left: val });
  }
  if (type === "top") {
    activeObject.set({ top: val });
  }
  if (type === "flip") {
    activeObject.flip = e;
  }
  if (type === "border_width") {
    changeBorderWidth(e);
  }
  if (type === "rotateEnum") {
    const isSet = Math.abs(activeObject.rotateEnum - e) === 2;
    const rotateEnumOld = activeObject.rotateEnum;
    activeObject.set({ rotateEnum: e, rotateEnumOld, angle: angleEnum[e || 0] });

    setOrigin(activeObject, isSet ? "noSet" : "select");

    selectionUpdated({ target: activeObject });
  }
  setTimeout(() => {
    boundingDetection(activeObject, true);
  }, 100);

  canvas.renderAll();
};

const changeItemScale = async () => {
  let activeObjects = canvas.getActiveObjects();
  let activeObject = activeObjects[0];
  if (!activeObject || activeObjects.length > 1) return;

  let objectCamera = pageData.cameraList.find((item: any) => item.device === activeObject.device);

  let cameraItem = { width: 1920, height: 1080 };

  if (!objectCamera) return;
  if (objectCamera.cameraType === 3) {
    return feedback.msgError(t("stream.该相机不支持缩放调整"));
  }

  if (objectCamera.cameraType === 2) {
    cameraItem = { width: 1280, height: 720 };
  }

  if (objectCamera.cameraType === 1) {
    CAMERA_TYPE_LIST.forEach((item: any) => {
      if (item.device === objectCamera.device) {
        if (item.resolution === "N/A") {
          return feedback.msgError(t("stream.该相机不支持缩放调整"));
        }
        if (item.resolution === "1280*720") {
          cameraItem = { width: 1280, height: 720 };
        }
        if (item.resolution === "1920*1080") {
          cameraItem = { width: 1920, height: 1080 };
        }
      }
    });
  }

  showScale.value = true;
  await nextTick();

  let params = {
    cameraName: activeObject.device,
    containerWidth: 0,
    containerHeight: 0,
    rotateEnum: activeObject.rotateEnum || 0,
    boxParams: {},
  };

  if (activeObject.rotateEnum % 2 === 1) {
    params.containerWidth = cameraItem.height;
    params.containerHeight = cameraItem.width;
  } else {
    params.containerWidth = cameraItem.width;
    params.containerHeight = cameraItem.height;
  }

  for (const key in pageData.initCameraList) {
    if (Object.prototype.hasOwnProperty.call(pageData.initCameraList, key)) {
      const element = pageData.initCameraList[key];
      if (element.device === params.cameraName) {
        if (element.crop_height !== null && element.crop_height !== undefined) {
          const { crop_width, crop_height, crop_left, crop_top } = element;
          params.boxParams = {
            crop_width,
            crop_height,
            crop_left,
            crop_top,
          };
        }
      }
    }
  }
  console.log(params);

  scaleRef.value?.setParams(params);
  scaleRef.value?.open();
};

const handleScaleSubmit = (data: any) => {
  const { cameraName, ...params } = data;
  for (const key in pageData.initCameraList) {
    if (Object.prototype.hasOwnProperty.call(pageData.initCameraList, key)) {
      const element = pageData.initCameraList[key];
      if (element.device === cameraName) {
        Object.assign(element, params);
      }
    }
  }
  feedback.msgWarning(t("stream.点击应用后生效"));
};

// 删除元素
const deleteObj = async (value: String, isTips: Boolean = false) => {
  if (isTips) await feedback.confirm(t("vehicle.确定要删除"));

  canvas.getObjects().forEach((item: any) => {
    if (item.device === value) {
      canvas.remove(item);
    }
  });

  canvas.renderAll();
  setTimeout(() => {
    getCanvasDevice(); // 重新获取画布元素
  }, 100);
};

const addObj = (item: any) => {
  drawObj(item);
  setTimeout(() => {
    getCanvasDevice(); // 重新获取画布元素
  }, 100);
};

const addDefaultVideo = (item: any) => {
  const { device, detail } = item;
  const cameraItem = {
    device,
    detail,
    width: 480,
    height: 270,
    left: 120,
    top: 120,
    rotate: 0,
    flip: 0,
    zIndex: pageData.maxIndex + 1,
    border_color: "#FF5F00",
    border_width: 4,
  };
  drawObj(cameraItem);
  setTimeout(() => {
    getCanvasDevice();
  }, 300);
};

const setMainVideo = (item: any) => {
  deleteObj(item.device, false);

  pageData.main.detail && addDefaultVideo(pageData.main);
  pageData.main.detail = item.detail;
  pageData.main.device = item.device;

  setTimeout(() => {
    getCanvasDevice(); // 重新获取画布元素
  }, 100);
};

// 重新获取画布上元素
const getCanvasDevice = async () => {
  pageData.canvasObjects = [];
  await canvas.getObjects().forEach((item: any) => {
    if (item.device) {
      pageData.canvasObjects.push(item.device);
    }
  });
};

// 获取画布上元素
const getCanvasObjs = () => {
  const itemArr = canvas.getObjects().map((item: any) => {
    boundingDetection(item, true);
    return {
      device: item.device,
      width: Math.round(item.width * item.scaleX * pageData.resolution),
      height: Math.round(item.height * item.scaleY * pageData.resolution),
      left: Math.round(item.left * pageData.resolution),
      top: Math.round(item.top * pageData.resolution),
      rotate: item.rotateEnum,
      flip: item.flip,
      zIndex: item.zIndex,
      z_index: item.z_index,
      primary: item.primary,
      border_width: item.border_width,
      border_color: item.border_color,
    };
  });

  // 取双数
  itemArr.forEach((item: any) => {
    if (item.width % 2 == 1) item.width -= 1;
    if (item.height % 2 == 1) item.height -= 1;
    if (item.left % 2 == 1) item.left -= 1;
    if (item.top % 2 == 1) item.top -= 1;
    // if (item.border_width % 2 == 1) item.border_width -= 1;
  });

  let isLayoutEle = itemArr
    .filter((item: any) => {
      return item.device;
    })
    .map((item: any) => item.device);

  let cameraArr = Object.values(JSON.parse(JSON.stringify(pageData.cameraList))).filter(
    (item: any) => {
      return isLayoutEle.includes(item.device);
    }
  );

  cameraArr.forEach((item: any) => {
    itemArr.forEach((element: any) => {
      if (item.device === element.device) {
        Object.assign(item, element);
      }
    });
  });

  return cameraArr;
};

const getLayoutData = async () => {
  const { lists } = await layoutList(pageData.vehicle_id);
  pageData.layoutList = lists;

  setTimeout(() => {
    getSubmitParams();
    checkCurLayout();
  }, 500);
};

const checkCurLayout = () => {
  const curData: any = JSON.stringify(pageData.curRow.camera_list);
  pageData.layoutList.forEach((item: any) => {
    if (JSON.stringify(item.camera_list) === curData) {
      pageData.curLayoutName = item.name;
      pageData.layoutName = item.name;
    }
  });
};

const getCameraList = async () => {
  let params = { vehicle_id: pageData.vehicle_id };
  const { lists: list } = await getEnableCameraList(params);
  pageData.cameraList = list.map((item: any) => {
    return {
      device: item.camera_id,
      detail: item.camera_name,
      model: item.camera_model,
      cameraType: item.camera_type,
    };
  });
};

const handleSave = async () => {
  await getSubmitParams();
  if (pageData.layoutName === "") return feedback.msgError(t("stream.请输入布局名称"));

  const layoutData: any = {
    vehicle_id: pageData.vehicle_id,
    name: pageData.layoutName,
    camera_list: pageData.curRow.camera_list,
  };

  if (pageData.dialogType === "edit") {
    await layoutEdit(layoutData);
  } else {
    await layoutAdd(layoutData);
  }

  feedback.msgSuccess(t("common.操作成功"));
  pageData.dialogFormVisible = false;
  pageData.layoutName = "";
  pageData.curLayoutName = "";
  getLayoutData();
};

const deleteLayout = async (name: string) => {
  await feedback.confirm(t("vehicle.确定要删除"));
  await layoutDelete({ vehicle_id: pageData.vehicle_id, template_name: name });
  feedback.msgSuccess(t("common.操作成功"));
  getLayoutData();
};

const useLayout = (item: any) => {
  pageData.curLayoutName = item.name;
  pageData.layoutName = item.name;
  canvas.dispose();
  init({ camera_list: item.camera_list });
};

const newLayout = () => {
  let data: any = {
    camera_list: {},
  };

  let initLeft = 50;
  let initTop = 0;

  pageData.cameraList.forEach((item: any, index: any) => {
    let obj: any = {
      device: item.device,
      detail: item.detail,
      width: 480,
      height: 270,
      left: initLeft,
      top: initTop,
      rotate: 0,
      flip: 0,
      border_width: 4,
    };
    if (index === 0) {
      obj.z_index = 0;
      obj.primary = 1;
      obj.width = 1920;
      obj.height = 1080;
      obj.left = 0;
      obj.top = 0;
    } else {
      initTop += 330;
      if (initTop > 1000 - 330) {
        initTop = 0;
        initLeft += 530;
      }
    }
    data.camera_list[`camera_${index}`] = obj;
  });

  pageData.curLayoutName = "";
  pageData.curLayoutName = "";
  pageData.layoutName = "";
  canvas.dispose();
  init(data);
};

const ShowSaveDialog = async (e: any, type: string) => {
  pageData.dialogType = type;
  if (type == "save") {
    pageData.layoutName === "";
    pageData.dialogFormTitle = t("stream.另存为新布局");
  } else if (type == "edit") {
    pageData.dialogFormTitle = t("stream.更新当前布局");
  }
  const target = e.target;
  if (target.nodeName === "SPAN") {
    target.parentNode.blur();
  }
  target.blur();
  pageData.dialogFormVisible = true;
};

function convertArrayToObject(key: string, array: any) {
  var result: any = {};
  for (var i = 0; i < array.length; i++) {
    result[key + i] = array[i];
  }
  return result;
}

const getSubmitParams = async () => {
  let itemArr = await getCanvasObjs();

  let cameraList = itemArr.map((item: any) => {
    if (pageData.consoleType === "1") {
      if (item.left < pageData.layoutEdge) item.left = pageData.layoutEdge;
      let w = item.rotate % 2 === 1 ? item.height : item.width;
      if (item.left + w > 1920 - pageData.layoutEdge) item.left = 1920 - pageData.layoutEdge - w;
    }
    return item;
  });

  let cameraListSort = cameraList.sort((a: any, b: any) => {
    return a.zIndex - b.zIndex;
  });

  pageData.curRow.camera_list = convertArrayToObject("camera_", [pageData.main, ...cameraListSort]);

  for (const key in pageData.curRow.camera_list) {
    if (Object.prototype.hasOwnProperty.call(pageData.curRow.camera_list, key)) {
      const element = pageData.curRow.camera_list[key];
      delete element.zIndex;
    }
  }
  console.log(pageData.curRow.camera_list);
};

const handleUpdateLayout = async () => {
  if (pageData.curLayoutName !== "") {
    await getSubmitParams();
    if (!checkParams()) return;
    const layoutData: any = {
      vehicle_id: pageData.vehicle_id,
      name: pageData.curLayoutName,
      camera_list: pageData.curRow.camera_list,
    };
    try {
      await layoutEdit(layoutData);
      await getLayoutData();
      feedback.msgSuccess(t("common.操作成功"));
    } catch (error) {
      console.log(error);
    }
  }
};

const handleSubmit = async () => {
  await getSubmitParams();
  if (!checkParams()) return;
  await editRosParams(pageData.curRow);
  await handleClose();
};

const checkParams = () => {
  if (pageData.curRow.camera_list["camera_0"]?.device == "") {
    feedback.msgError("Please set the main camera");
    return false;
  } else {
    return true;
  }
};

const handleClose = () => {
  pageData.curLayoutName = "";
  pageData.layoutName = "";
  emit("close");
  //@ts-ignore
  if (window.androidAction)
    //@ts-ignore
    window.androidAction !== "undefined" && window.androidAction.closeView();
};

const editRosParams = async (obj: any) => {
  // 附加参数融合,找出相同的相机
  const scaleKeyList = ["crop_left", "crop_top", "crop_width", "crop_height"];
  const objCameraList = obj.camera_list;
  const initCameraList = pageData.initCameraList;

  for (const curKey in objCameraList) {
    const objCameraItem = objCameraList[curKey];
    for (const initItemKey in initCameraList) {
      const initCameraItem = initCameraList[initItemKey];
      if (objCameraItem["device"] === initCameraItem["device"]) {
        for (const childKey in initCameraItem) {
          if (
            !Object.prototype.hasOwnProperty.call(objCameraItem, childKey) ||
            scaleKeyList.indexOf(childKey) !== -1
          ) {
            objCameraList[curKey][childKey] = initCameraList[initItemKey][childKey];
          }
        }
      }
    }
  }

  const params = {
    vehicle_id: pageData.vehicle_id,
    camera_list: objCameraList,
  };
  await editMixLayout(params);
  feedback.msgSuccess(t("common.操作成功"));
};

const handleTabChange = (tab: string) => {
  activeName.value = tab;
};
</script>

<style scoped lang="scss">
.bux-button {
  border-radius: 4px;
  min-width: 60px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 14px;
  cursor: pointer;
}

.bux-button-cancel {
  background-color: #000;
  color: #fff;
  border: 2px solid #fff;
}

.bux-button-confirm {
  background-color: #ff5f00;
  color: #fff;
  border: 2px solid #ff5f00;
}

.bux-button-primary {
  background-color: #000;
  color: #ff5f00;
  border: 2px solid #ff5f00;
}

.tabs-content {
  margin-left: 12px;
  margin-top: 16px;
  height: 404px;
}

.edit-popup {
  max-width: 1080px;
  margin: 0 auto;
  background-color: #000;
  padding: 0px;
}

.aid-wrap {
  background-color: #6a6b6a;
  border-radius: 8px;
}

:deep(.el-tabs__item) {
  color: #fff;
}

:deep(.el-form-item__content) {
  span {
    color: #fff;
  }
}

:deep(.tab-wrap) {
  display: flex;
  flex: 1;
  justify-content: center;

  .tabs .bux-button {
    padding: 0 6px;
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    background-color: transparent;

    .el-input__inner {
      color: #fff;
    }
  }

  .el-input-number__decrease {
    background-color: transparent;
    color: #fff;
  }

  .el-input-number__increase {
    background-color: transparent;
    color: #fff;
  }
}

:deep(.el-tabs__item) {
  padding: 0 10px;
}

:deep(.el-form-item__label) {
  color: rgb(228, 226, 226);
}

:deep(.el-tabs__active-bar) {
  width: 60px;
}

:deep(.el-select__wrapper) {
  background-color: transparent;

  .el-select__placeholder {
    color: #fff;
  }
}

.save-layout {
  :deep(.el-dialog__body) {
    padding: 15px 20px 0 20px;
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    padding-left: 32px;
    padding-right: 32px;
  }
}
</style>
