<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="550px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="84px" :rules="formRules">
        <el-form-item label="账号" prop="username">
          <el-input v-model="formData.username" :disabled="mode === 'edit'" placeholder="请输入账号" clearable />
        </el-form-item>
        <el-form-item label="名称" prop="nickname">
          <el-input v-model="formData.nickname" placeholder="请输入名称" clearable />
        </el-form-item>
        <el-form-item label="角色" prop="role_ids">
          <el-select
            multiple
            v-model="formData.role_ids"
            :disabled="isRoot"
            class="flex-1"
            clearable
            placeholder="请选择角色"
          >
            <el-option v-if="isRoot" label="系统人员" value="0" />
            <el-option
              v-for="(item, index) in optionsData.role.lists"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="mode === 'add'" label="密码" prop="password">
          <el-input v-model.trim="formData.password" show-password clearable placeholder="请输入密码" />
        </el-form-item>

        <el-form-item v-if="mode === 'add'" label="确认密码" prop="passwordConfirm">
          <el-input v-model.trim="formData.passwordConfirm" show-password clearable placeholder="请输入确认密码" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model.trim="formData.email" clearable placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="人员状态" v-if="!isRoot">
          <el-switch v-model="formData.status" :active-value="0" :inactive-value="1" />
        </el-form-item>

        <!-- <el-form-item label="多处登录">
          <div>
            <el-switch v-model="formData.isMultipoint" :active-value="1" :inactive-value="0" />
            <div class="form-tips">允许多人同时在线登录</div>
          </div>
        </el-form-item> -->
      </el-form>
    </popup>
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import { adminAdd, adminEdit, adminDetail } from "@/api/perms/admin";
import { useDictOptions } from "@/hooks/useDictOptions";
import { roleAll } from "@/api/perms/role";
import feedback from "@/utils/feedback";
const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑人员" : "新增人员";
});

const formData = reactive({
  id: "",
  username: "",
  nickname: "",
  role_ids: [],
  avatar: "",
  password: "",
  passwordConfirm: "",
  email: "",
  status: 0,
  isMultipoint: 1,
});

const isRoot = computed(() => {
  return formData.id == "1";
});

const passwordConfirmValidator = (rule: object, value: string, callback: any) => {
  if (formData.password) {
    if (!value) callback(new Error("请再次输入密码"));
    if (value !== formData.password) callback(new Error("两次输入密码不一致!"));
  }
  callback();
};
const formRules = reactive({
  username: [
    {
      required: true,
      message: "请输入账号",
      trigger: ["blur"],
    },
  ],
  nickname: [
    {
      required: true,
      message: "请输入名称",
      trigger: ["blur"],
    },
  ],
  role_ids: [
    {
      required: true,
      message: "请选择角色",
      trigger: ["blur"],
    },
  ],
  password: [
    {
      required: true,
      message: "请输入密码",
      trigger: "blur",
    },
    {
      min: 8,
      message: "密码长度至少为8位",
      trigger: "blur",
    },
  ] as any[],
  email: [
    {
      required: true,
      message: "请输入邮箱",
      trigger: "blur",
    },
  ] as any[],
  passwordConfirm: [
    {
      required: true,
      message: "请再次输入密码",
      trigger: "blur",
    },
    {
      validator: passwordConfirmValidator,
      trigger: "blur",
    },
  ] as any[],
});

const { optionsData } = useDictOptions<{
  role: {
    lists: any[];
  };
  post: any[];
  dept: any[];
}>({
  role: {
    api: roleAll,
  },
});

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit" ? await adminEdit(formData) : await adminAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (row: any) => {
  const data = await adminDetail({
    uid: row.id,
  });
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      //@ts-ignore
      formData[key] = data[key];
    }
  }
  // formRules.password = [];
  // formRules.passwordConfirm = [
  //   {
  //     validator: passwordConfirmValidator,
  //     trigger: "blur",
  //   },
  // ];
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
