<!-- 个人资料 -->
<template>
  <div class="user-setting">
    <el-card class="!border-none" shadow="never">
      <div class="text-[16px] font-semibold mb-4 flex items-center justify-between">
        <div>令牌管理</div>
        <el-button class="ml-2" type="primary" link @click="handleCreate">创建令牌</el-button>
      </div>
      <el-table :data="apiKeyList" border :height="186">
        <el-table-column label="令牌" prop="token" :width="420" />
        <el-table-column label="名称" prop="description" />
        <el-table-column label="创建时间" prop="create_time" :width="200">
          <template #default="{ row }">
            <span>{{ dayjs.utc(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="120">
          <template #default="{ row }">
            <el-button type="text" @click="handleCopy(row)">复制</el-button>
            <el-button type="text" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-card class="!border-none mt-4" shadow="never">
      <div class="text-[16px] font-semibold mb-4">
        <span>密码修改</span>
      </div>
      <el-form ref="formRef" class="ls-form" :model="formData" :rules="rules" label-width="100px">
        <el-form-item :label="$t('common.账号')" prop="username">
          <div class="w-80">
            <el-input v-model="formData.username" disabled />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.名称')" prop="nickname">
          <div class="w-80">
            <el-input v-model="formData.nickname" :placeholder="$t('common.请输入名称')" />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.当前密码')" prop="oldPassword">
          <div class="w-80">
            <el-input
              v-model.trim="formData.oldPassword"
              :placeholder="$t('common.修改密码时必填')"
              type="password"
              show-password
            />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.新的密码')" prop="newPassword">
          <div class="w-80">
            <el-input
              v-model.trim="formData.newPassword"
              :placeholder="$t('common.修改密码时必填')"
              type="password"
              show-password
            />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.确定密码')" prop="passwordConfirm">
          <div class="w-80">
            <el-input
              v-model.trim="formData.passwordConfirm"
              :placeholder="$t('common.修改密码时必填')"
              type="password"
              show-password
            />
          </div>
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </el-card>

    <!-- 创建令牌弹窗 -->
    <el-dialog v-model="dialogVisible" title="创建令牌" width="500px">
      <el-form ref="apiFormRef" :model="apiFormData" :rules="apiRules" label-width="100px">
        <el-form-item label="描述" prop="description">
          <el-input v-model="apiFormData.description" placeholder="请输入令牌描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleApiSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="userSetting">
import { setUserInfo, setUserPassword, getApiKeyList, addApiKey, delApiKey } from "@/api/user";
import useUserStore from "@/stores/modules/user";
import feedback from "@/utils/feedback";
import type { FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();
const formRef = ref<FormInstance>();
const apiFormRef = ref<FormInstance>();
const userStore = useUserStore();
// 表单数据
const formData = reactive({
  id: "", // id
  username: "", // 账号
  nickname: "", // 名称
  oldPassword: "", // 当前密码
  newPassword: "", // 新的密码
  passwordConfirm: "", // 确定密码
});
const apiKeyList = ref([]);
// 创建令牌弹窗
const dialogVisible = ref(false);
const apiFormData = reactive({
  description: "",
});
// API表单校验规则
const apiRules = reactive({
  description: [
    {
      required: true,
      message: "请输入令牌描述",
      trigger: "blur",
    },
  ],
});
// 表单校验规则
const rules = reactive<object>({
  nickname: [
    {
      required: true,
      message: t("common.请输入名称"),
      trigger: ["blur"],
    },
  ],
  oldPassword: [
    {
      validator: (rule: object, value: string, callback: any) => {
        // if (formData.oldPassword) callback(new Error("请输入当前密码"));
        callback();
      },
      trigger: "blur",
    },
  ],
  newPassword: [
    {
      validator: (rule: object, value: string, callback: any) => {
        if (formData.oldPassword) {
          if (!formData.newPassword) callback(new Error(t("common.请输入新的密码")));
          if (formData.newPassword.length < 8) callback(new Error(t("common.密码长度不能小于8位")));
        }
        callback();
      },
      trigger: "blur",
    },
  ],
  passwordConfirm: [
    {
      validator: (rule: object, value: string, callback: any) => {
        if (formData.passwordConfirm !== formData.newPassword)
          callback(new Error(t("common.两次输入密码不一致!")));
        callback();
      },
      trigger: "blur",
    },
  ],
});
// 获取个人设置
const getUser = async () => {
  const userInfo = userStore.userInfo;
  for (const key in formData) {
    //@ts-ignore
    formData[key] = userInfo[key];
  }
};
const getApiKey = async () => {
  const { lists } = await getApiKeyList();
  apiKeyList.value = lists;
};
// 设置个人设置
const setUser = async () => {
  if (formData.passwordConfirm) {
    await setUserPassword(formData);
  } else {
    await setUserInfo(formData);
  }
  userStore.logout();
  feedback.msgSuccess(t("common.操作成功"));
  userStore.getUserInfo();
};
// 提交数据
const handleSubmit = async () => {
  await formRef.value?.validate();
  setUser();
};
// 打开创建令牌弹窗
const handleCreate = () => {
  dialogVisible.value = true;
  apiFormData.description = "";
};
// 提交创建令牌
const handleApiSubmit = async () => {
  await apiFormRef.value?.validate();
  await addApiKey(apiFormData);
  feedback.msgSuccess("创建成功");
  dialogVisible.value = false;
  getApiKey(); // 刷新列表
};
// 复制令牌
const handleCopy = async (row: any) => {
  await navigator.clipboard.writeText(row.token);
  feedback.msgSuccess("复制成功");
};
const handleDelete = async (row: any) => {
  await feedback.confirm("是否确认删除?");
  await delApiKey({ token: row.token });
  getApiKey();
};
getUser();
getApiKey();
</script>

<style lang="scss" scoped></style>
