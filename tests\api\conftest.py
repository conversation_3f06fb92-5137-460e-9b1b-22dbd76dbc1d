import os

import pytest


# 覆盖默认配置，切到测试环境
os.environ["OMS_MODE"] = "test"


@pytest.fixture(scope="session")
def app_config():
    from apps.config import get_settings

    yield get_settings()


@pytest.fixture(scope="session")
def rdb(app_config):
    from redis import Redis

    rdb_: Redis = Redis(
        host=app_config.redis_host,
        port=app_config.redis_port,
        db=app_config.redis_db,
        password=app_config.redis_password,
        encoding="utf-8",
        decode_responses=True,
    )
    assert rdb_.ping() is True
    rdb_.flushdb()
    yield rdb_
    rdb_.close()


@pytest.fixture(scope="session")
def mdb(app_config):
    from pymongo import MongoClient

    uri = "mongodb://{}:{}@{}:{}/{}?authSource=admin".format(
        app_config.mongo_username,
        app_config.mongo_password,
        app_config.mongo_host,
        app_config.mongo_port,
        app_config.mongo_db_name,
    )
    mdb_ = MongoClient(uri, timeoutMS=2000)
    assert mdb_.server_info() is not None
    mdb_.drop_database(app_config.mongo_db_name)
    test_db = mdb_[app_config.mongo_db_name]
    yield test_db
    mdb_.close()


@pytest.fixture(scope="session")
def app_run(rdb, mdb):
    """启动 uvicorn 服务"""
    from apps import create_app

    yield create_app()
