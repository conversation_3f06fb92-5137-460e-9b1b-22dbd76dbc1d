import pytest

from starlette.testclient import TestClient


def user_login(client: TestClient, username, password) -> str:
    """登录"""
    res = client.post("/user/login", json={"username": username, "password": password})
    assert res.status_code == 200
    assert res.json()["code"] == 1000
    return res.json()["data"]["token"]


@pytest.fixture(scope="session")
def client(app_run):
    """v2客户端"""
    with TestClient(app_run) as ci:
        ci.base_url = "http://test/api/v2"
        yield ci


@pytest.fixture(scope="session")
def client_admin(app_run):
    """v2客户端"""
    with TestClient(app_run) as ci:
        ci.base_url = "http://test/api/v2"
        token = user_login(ci, "admin", "BuilderX@2024")
        ci.headers = {"Authorization": f"Bearer {token}"}
        yield ci
